export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.12 (cd3cf9e)"
  }
  public: {
    Tables: {
      banned_tickets: {
        Row: {
          action_type: string
          ban_reason: string
          banned_at: string | null
          banned_by_operator: string | null
          banned_by_user_id: number | null
          duration: number
          id: number
          is_refunded: boolean | null
          issued_at: string
          location_id: number | null
          location_name: string | null
          operator_name: string | null
          original_ticket_id: number
          payment_method: string
          photo_path: string | null
          refund_amount: number | null
          refunded_at: string | null
          refunded_by_operator: string | null
          refunded_by_user_id: number | null
          ticket_id: string
          user_id: number | null
        }
        Insert: {
          action_type: string
          ban_reason: string
          banned_at?: string | null
          banned_by_operator?: string | null
          banned_by_user_id?: number | null
          duration: number
          id?: number
          is_refunded?: boolean | null
          issued_at: string
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          original_ticket_id: number
          payment_method: string
          photo_path?: string | null
          refund_amount?: number | null
          refunded_at?: string | null
          refunded_by_operator?: string | null
          refunded_by_user_id?: number | null
          ticket_id: string
          user_id?: number | null
        }
        Update: {
          action_type?: string
          ban_reason?: string
          banned_at?: string | null
          banned_by_operator?: string | null
          banned_by_user_id?: number | null
          duration?: number
          id?: number
          is_refunded?: boolean | null
          issued_at?: string
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          original_ticket_id?: number
          payment_method?: string
          photo_path?: string | null
          refund_amount?: number | null
          refunded_at?: string | null
          refunded_by_operator?: string | null
          refunded_by_user_id?: number | null
          ticket_id?: string
          user_id?: number | null
        }
        Relationships: []
      }
      categories: {
        Row: {
          code: string
          created_at: string | null
          description: string | null
          display_order: number | null
          id: number
          name: string
          parent_id: number | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          id?: number
          name: string
          parent_id?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          id?: number
          name?: string
          parent_id?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      draft_sales: {
        Row: {
          created_at: string | null
          draft_sale_id: string
          id: number
          item_count: number
          location_id: number | null
          location_name: string | null
          operator_name: string | null
          status: string | null
          total_amount: number
          user_id: number | null
        }
        Insert: {
          created_at?: string | null
          draft_sale_id: string
          id?: number
          item_count: number
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          status?: string | null
          total_amount: number
          user_id?: number | null
        }
        Update: {
          created_at?: string | null
          draft_sale_id?: string
          id?: number
          item_count?: number
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          status?: string | null
          total_amount?: number
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "draft_sales_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "draft_sales_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      draft_sales_items: {
        Row: {
          discount: number | null
          draft_sale_id: string
          id: number
          product_barcode: string | null
          product_category: string | null
          product_id: number | null
          product_name: string
          product_subcategory: string | null
          quantity: number
          total_price: number
          unit_price: number
        }
        Insert: {
          discount?: number | null
          draft_sale_id: string
          id?: number
          product_barcode?: string | null
          product_category?: string | null
          product_id?: number | null
          product_name: string
          product_subcategory?: string | null
          quantity: number
          total_price: number
          unit_price: number
        }
        Update: {
          discount?: number | null
          draft_sale_id?: string
          id?: number
          product_barcode?: string | null
          product_category?: string | null
          product_id?: number | null
          product_name?: string
          product_subcategory?: string | null
          quantity?: number
          total_price?: number
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "draft_sales_items_draft_sale_id_fkey"
            columns: ["draft_sale_id"]
            isOneToOne: false
            referencedRelation: "draft_sales"
            referencedColumns: ["draft_sale_id"]
          },
          {
            foreignKeyName: "draft_sales_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      location_stocks: {
        Row: {
          created_at: string | null
          id: number
          location: string
          min_qty: number | null
          price: number | null
          product_id: number
          stock: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          location: string
          min_qty?: number | null
          price?: number | null
          product_id: number
          stock?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          location?: string
          min_qty?: number | null
          price?: number | null
          product_id?: number
          stock?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "location_stocks_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      locations: {
        Row: {
          address1: string | null
          address2: string | null
          app_mode: string | null
          company_name: string | null
          created_at: string | null
          deli: number | null
          email: string | null
          id: number
          location: string
          location_code: string
          phone: string | null
          status: string | null
          tax_percent: number | null
          theater_plu: string | null
          theater_time: string | null
          updated_at: string | null
        }
        Insert: {
          address1?: string | null
          address2?: string | null
          app_mode?: string | null
          company_name?: string | null
          created_at?: string | null
          deli?: number | null
          email?: string | null
          id?: number
          location: string
          location_code: string
          phone?: string | null
          status?: string | null
          tax_percent?: number | null
          theater_plu?: string | null
          theater_time?: string | null
          updated_at?: string | null
        }
        Update: {
          address1?: string | null
          address2?: string | null
          app_mode?: string | null
          company_name?: string | null
          created_at?: string | null
          deli?: number | null
          email?: string | null
          id?: number
          location?: string
          location_code?: string
          phone?: string | null
          status?: string | null
          tax_percent?: number | null
          theater_plu?: string | null
          theater_time?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      products: {
        Row: {
          barcode: string
          category: string | null
          color: string | null
          created_at: string | null
          daily_item: number | null
          description: string
          id: number
          image_confirm: number | null
          image_path: string | null
          max_qty: number | null
          min_qty: number | null
          non_scanable: number | null
          priority: number | null
          purchase_price: number | null
          size: string | null
          special_discount: number | null
          style: string | null
          subcategory: string | null
          supplier: string | null
          updated_at: string | null
        }
        Insert: {
          barcode: string
          category?: string | null
          color?: string | null
          created_at?: string | null
          daily_item?: number | null
          description: string
          id?: number
          image_confirm?: number | null
          image_path?: string | null
          max_qty?: number | null
          min_qty?: number | null
          non_scanable?: number | null
          priority?: number | null
          purchase_price?: number | null
          size?: string | null
          special_discount?: number | null
          style?: string | null
          subcategory?: string | null
          supplier?: string | null
          updated_at?: string | null
        }
        Update: {
          barcode?: string
          category?: string | null
          color?: string | null
          created_at?: string | null
          daily_item?: number | null
          description?: string
          id?: number
          image_confirm?: number | null
          image_path?: string | null
          max_qty?: number | null
          min_qty?: number | null
          non_scanable?: number | null
          priority?: number | null
          purchase_price?: number | null
          size?: string | null
          special_discount?: number | null
          style?: string | null
          subcategory?: string | null
          supplier?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          location_id: number | null
          role: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          full_name?: string | null
          id: string
          location_id?: number | null
          role?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          location_id?: number | null
          role?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      sales: {
        Row: {
          change_amount: number | null
          discount_amount: number | null
          id: number
          item_count: number
          location_id: number | null
          location_name: string | null
          operator_name: string | null
          payment_cash: number | null
          payment_credit: number | null
          payment_debit: number | null
          payment_total: number
          sale_date: string | null
          sale_id: string
          sale_type: string | null
          status: string | null
          subtotal: number
          tax_amount: number | null
          total_amount: number
          user_id: number | null
        }
        Insert: {
          change_amount?: number | null
          discount_amount?: number | null
          id?: number
          item_count: number
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          payment_cash?: number | null
          payment_credit?: number | null
          payment_debit?: number | null
          payment_total: number
          sale_date?: string | null
          sale_id: string
          sale_type?: string | null
          status?: string | null
          subtotal: number
          tax_amount?: number | null
          total_amount: number
          user_id?: number | null
        }
        Update: {
          change_amount?: number | null
          discount_amount?: number | null
          id?: number
          item_count?: number
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          payment_cash?: number | null
          payment_credit?: number | null
          payment_debit?: number | null
          payment_total?: number
          sale_date?: string | null
          sale_id?: string
          sale_type?: string | null
          status?: string | null
          subtotal?: number
          tax_amount?: number | null
          total_amount?: number
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "sales_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      sales_items: {
        Row: {
          id: number
          product_barcode: string | null
          product_category: string | null
          product_id: number | null
          product_name: string
          product_subcategory: string | null
          quantity: number
          sale_id: string
          total_price: number
          unit_price: number
        }
        Insert: {
          id?: number
          product_barcode?: string | null
          product_category?: string | null
          product_id?: number | null
          product_name: string
          product_subcategory?: string | null
          quantity: number
          sale_id: string
          total_price: number
          unit_price: number
        }
        Update: {
          id?: number
          product_barcode?: string | null
          product_category?: string | null
          product_id?: number | null
          product_name?: string
          product_subcategory?: string | null
          quantity?: number
          sale_id?: string
          total_price?: number
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "sales_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_items_sale_id_fkey"
            columns: ["sale_id"]
            isOneToOne: false
            referencedRelation: "sales"
            referencedColumns: ["sale_id"]
          },
        ]
      }
      shifts: {
        Row: {
          actual_end_time: string | null
          created_at: string | null
          id: number
          location_id: number
          location_name: string
          operator_name: string
          remaining_time_minutes: number | null
          shift_duration_hours: number | null
          shift_end_time: string
          shift_id: string
          shift_start_time: string | null
          status: string | null
          total_deli_sales: number | null
          total_deli_transactions: number | null
          total_sale_sales: number | null
          total_sale_transactions: number | null
          total_sales: number | null
          total_theater_sales: number | null
          total_theater_transactions: number | null
          total_transactions: number | null
          updated_at: string | null
          user_id: number
        }
        Insert: {
          actual_end_time?: string | null
          created_at?: string | null
          id?: number
          location_id: number
          location_name: string
          operator_name: string
          remaining_time_minutes?: number | null
          shift_duration_hours?: number | null
          shift_end_time: string
          shift_id: string
          shift_start_time?: string | null
          status?: string | null
          total_deli_sales?: number | null
          total_deli_transactions?: number | null
          total_sale_sales?: number | null
          total_sale_transactions?: number | null
          total_sales?: number | null
          total_theater_sales?: number | null
          total_theater_transactions?: number | null
          total_transactions?: number | null
          updated_at?: string | null
          user_id: number
        }
        Update: {
          actual_end_time?: string | null
          created_at?: string | null
          id?: number
          location_id?: number
          location_name?: string
          operator_name?: string
          remaining_time_minutes?: number | null
          shift_duration_hours?: number | null
          shift_end_time?: string
          shift_id?: string
          shift_start_time?: string | null
          status?: string | null
          total_deli_sales?: number | null
          total_deli_transactions?: number | null
          total_sale_sales?: number | null
          total_sale_transactions?: number | null
          total_sales?: number | null
          total_theater_sales?: number | null
          total_theater_transactions?: number | null
          total_transactions?: number | null
          updated_at?: string | null
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "shifts_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shifts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      suppliers: {
        Row: {
          address1: string | null
          address2: string | null
          city: string | null
          created_at: string | null
          email: string | null
          fax: string | null
          id: number
          name: string
          retail_website: string | null
          sales_rep: string | null
          sales_rep_phone: string | null
          state: string | null
          status: string | null
          telephone: string | null
          updated_at: string | null
          wholesale_website: string | null
          zip_code: string | null
        }
        Insert: {
          address1?: string | null
          address2?: string | null
          city?: string | null
          created_at?: string | null
          email?: string | null
          fax?: string | null
          id?: number
          name: string
          retail_website?: string | null
          sales_rep?: string | null
          sales_rep_phone?: string | null
          state?: string | null
          status?: string | null
          telephone?: string | null
          updated_at?: string | null
          wholesale_website?: string | null
          zip_code?: string | null
        }
        Update: {
          address1?: string | null
          address2?: string | null
          city?: string | null
          created_at?: string | null
          email?: string | null
          fax?: string | null
          id?: number
          name?: string
          retail_website?: string | null
          sales_rep?: string | null
          sales_rep_phone?: string | null
          state?: string | null
          status?: string | null
          telephone?: string | null
          updated_at?: string | null
          wholesale_website?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      tickets: {
        Row: {
          duration: number
          id: number
          issued_at: string | null
          location_id: number | null
          location_name: string | null
          operator_name: string | null
          payment_method: string
          photo_path: string | null
          status: string | null
          ticket_id: string
          ticket_price: number | null
          total_amount: number | null
          user_id: number | null
        }
        Insert: {
          duration: number
          id?: number
          issued_at?: string | null
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          payment_method: string
          photo_path?: string | null
          status?: string | null
          ticket_id: string
          ticket_price?: number | null
          total_amount?: number | null
          user_id?: number | null
        }
        Update: {
          duration?: number
          id?: number
          issued_at?: string | null
          location_id?: number | null
          location_name?: string | null
          operator_name?: string | null
          payment_method?: string
          photo_path?: string | null
          status?: string | null
          ticket_id?: string
          ticket_price?: number | null
          total_amount?: number | null
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "tickets_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tickets_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_permissions: {
        Row: {
          can_delete: number | null
          can_edit: number | null
          can_view: number | null
          created_at: string | null
          id: number
          module_id: string
          module_name: string
          user_id: number
        }
        Insert: {
          can_delete?: number | null
          can_edit?: number | null
          can_view?: number | null
          created_at?: string | null
          id?: number
          module_id: string
          module_name: string
          user_id: number
        }
        Update: {
          can_delete?: number | null
          can_edit?: number | null
          can_view?: number | null
          created_at?: string | null
          id?: number
          module_id?: string
          module_name?: string
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_permissions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string | null
          id: number
          last_login: string | null
          location_id: number | null
          name: string | null
          password: string
          role: string
          status: string | null
          username: string
        }
        Insert: {
          created_at?: string | null
          id?: number
          last_login?: string | null
          location_id?: number | null
          name?: string | null
          password: string
          role: string
          status?: string | null
          username: string
        }
        Update: {
          created_at?: string | null
          id?: number
          last_login?: string | null
          location_id?: number | null
          name?: string | null
          password?: string
          role?: string
          status?: string | null
          username?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_users_location_id"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      disable_triggers: {
        Args: { table_name: string }
        Returns: undefined
      }
      reset_sequence: {
        Args: { table_name: string; max_id: number }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
