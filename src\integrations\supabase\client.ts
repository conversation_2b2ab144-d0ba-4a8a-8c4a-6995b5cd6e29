// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ctxqgwphsimygwdshnun.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN0eHFnd3Boc2lteWd3ZHNobnVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzMjY0OTIsImV4cCI6MjA2ODkwMjQ5Mn0.Unj9DiBfri_7nzbkdp1JE13DklRm67iUrcNlBsF2lZU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});