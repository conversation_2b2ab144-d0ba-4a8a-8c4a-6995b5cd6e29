import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Store, MapPin, Users, DollarSign, RefreshCw } from "lucide-react";
import { useLocationPerformance } from "@/hooks/useDashboardData";

// Currency and number formatters
const currencyFormatter = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(value);
};

const numberFormatter = (value: number) => {
  return new Intl.NumberFormat('en-US').format(value);
};

export default function Shops() {
  const { data: locationData, loading, error, refetch } = useLocationPerformance();
  const handleRefresh = () => {
    refetch();
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Shops</h2>
            <p className="text-muted-foreground">
              Manage all your retail locations and monitor their performance.
            </p>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center">
              <p className="text-destructive font-medium">Failed to load shops</p>
              <p className="text-sm text-muted-foreground mt-1">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Shops</h2>
          <p className="text-muted-foreground">
            Manage all your retail locations and monitor their performance.
          </p>
        </div>
        <Button onClick={handleRefresh} variant="outline" disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {loading ? (
        <div className="grid gap-6 md:grid-cols-2">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-[400px] w-full rounded-lg" />
          ))}
        </div>
      ) : locationData.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center">
              <Store className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">No shops found</p>
              <p className="text-sm text-muted-foreground mt-1">Add your first shop to get started</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          {locationData.map((location) => (
            <Card key={location.locationId} className="group relative overflow-hidden border-0 bg-gradient-to-br from-white via-gray-50/50 to-blue-50/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
              {/* Decorative gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <CardHeader className="relative pb-4 bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 text-white">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-xl font-bold tracking-tight">
                      {location.locationName}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <div className="px-2 py-1 bg-white/20 rounded-full text-xs font-medium backdrop-blur-sm">
                        {location.locationCode}
                      </div>
                      <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" />
                      <span className="text-xs text-emerald-200">Active</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent">
                      {currencyFormatter(location.totalRevenue)}
                    </div>
                    <div className="text-sm text-blue-200 font-medium">
                      Total Revenue
                    </div>
                  </div>
                </div>

                {/* Decorative bottom border */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-emerald-400" />
              </CardHeader>

              <CardContent className="relative space-y-6 p-6">
                {/* Revenue Section */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="group/item relative p-4 rounded-xl bg-gradient-to-br from-blue-50 to-blue-100/50 border border-blue-200/50 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-blue-700">
                          Monthly Revenue
                        </div>
                        <div className="text-xl font-bold text-blue-900">
                          {currencyFormatter(location.monthlyRevenue)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="group/item relative p-4 rounded-xl bg-gradient-to-br from-emerald-50 to-emerald-100/50 border border-emerald-200/50 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-lg bg-emerald-500 flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-emerald-700">
                          Ticket Revenue
                        </div>
                        <div className="text-xl font-bold text-emerald-900">
                          {currencyFormatter(location.ticketRevenue)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Metrics Section */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 rounded-xl bg-gradient-to-br from-violet-50 to-violet-100/50 border border-violet-200/50 hover:shadow-md transition-all duration-200 group/metric">
                    <div className="w-12 h-12 mx-auto mb-2 rounded-full bg-violet-500 flex items-center justify-center group-hover/metric:scale-110 transition-transform duration-200">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                    <div className="text-2xl font-bold text-violet-900 mb-1">
                      {location.operatorCount}
                    </div>
                    <div className="text-xs font-medium text-violet-700">
                      Operators
                    </div>
                  </div>

                  <div className="text-center p-4 rounded-xl bg-gradient-to-br from-amber-50 to-amber-100/50 border border-amber-200/50 hover:shadow-md transition-all duration-200 group/metric">
                    <div className="w-12 h-12 mx-auto mb-2 rounded-full bg-amber-500 flex items-center justify-center group-hover/metric:scale-110 transition-transform duration-200">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 9h.01M15 9h.01M9 15h.01M15 15h.01" />
                      </svg>
                    </div>
                    <div className="text-2xl font-bold text-amber-900 mb-1">
                      {numberFormatter(location.itemsSold)}
                    </div>
                    <div className="text-xs font-medium text-amber-700">
                      Items Sold
                    </div>
                  </div>

                  <div className="text-center p-4 rounded-xl bg-gradient-to-br from-rose-50 to-rose-100/50 border border-rose-200/50 hover:shadow-md transition-all duration-200 group/metric">
                    <div className="w-12 h-12 mx-auto mb-2 rounded-full bg-rose-500 flex items-center justify-center group-hover/metric:scale-110 transition-transform duration-200">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                      </svg>
                    </div>
                    <div className="text-2xl font-bold text-rose-900 mb-1">
                      {location.ticketsSold}
                    </div>
                    <div className="text-xs font-medium text-rose-700">
                      Tickets Sold
                    </div>
                  </div>
                </div>

                {/* Performance Indicator */}
                <div className="flex items-center justify-between p-3 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-sm font-medium text-gray-700">Performance Status</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-sm font-bold text-green-700">Excellent</span>
                    <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}