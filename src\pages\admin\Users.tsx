import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Search, UserPlus, MoreHorizontal, Shield, User, Crown, MapPin, Calendar, Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface UserData {
  id: number;
  username: string;
  name: string | null;
  role: string;
  status: string;
  location_id: number | null;
  created_at: string;
  last_login: string | null;
  location?: {
    location: string;
    location_code: string;
  };
}

const getRoleIcon = (role: string) => {
  switch (role) {
    case "Admin":
      return <Crown className="h-4 w-4 text-yellow-600" />;
    case "Manager":
      return <Shield className="h-4 w-4 text-blue-600" />;
    default:
      return <User className="h-4 w-4 text-gray-600" />;
  }
};

const getRoleBadgeVariant = (role: string) => {
  switch (role) {
    case "Admin":
      return "destructive";
    case "Manager":
      return "default";
    default:
      return "secondary";
  }
};

export default function Users() {
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();

  // Fetch users data with location information
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          name,
          role,
          status,
          location_id,
          created_at,
          last_login,
          locations:location_id (
            location,
            location_code
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching users:', error);
        toast({
          title: "Error",
          description: "Failed to fetch users data",
          variant: "destructive",
        });
        return;
      }

      // Transform the data to match our interface
      const transformedUsers = data?.map(user => ({
        ...user,
        location: user.locations
      })) || [];

      setUsers(transformedUsers);
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Set up real-time subscription
  useEffect(() => {
    fetchUsers();

    // Subscribe to real-time changes
    const subscription = supabase
      .channel('users_changes')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users'
        },
        (payload) => {
          console.log('Real-time update:', payload);
          fetchUsers(); // Refetch data when changes occur
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Filter users based on search term
  const filteredUsers = users.filter(user =>
    user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.location?.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate statistics
  const totalUsers = users.length;
  const activeUsers = users.filter(user => user.status === 'active').length;
  const adminUsers = users.filter(user => user.role === 'Admin').length;
  const managerUsers = users.filter(user => user.role === 'Manager').length;

  // Format date helper
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)} days ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Users</h2>
          <p className="text-muted-foreground">
            Manage user accounts, roles, and permissions across your organization.
          </p>
        </div>
        <Button>
          <UserPlus className="h-4 w-4 mr-2" />
          Add New User
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : totalUsers}</div>
            <p className="text-xs text-muted-foreground">registered users</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
              <Badge className="text-xs bg-green-100 text-green-800">{loading ? "..." : activeUsers}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{loading ? "..." : activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              {loading ? "..." : `${totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0}% active rate`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Admins</CardTitle>
            <Crown className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : adminUsers}</div>
            <p className="text-xs text-muted-foreground">system administrators</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Managers</CardTitle>
            <Shield className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : managerUsers}</div>
            <p className="text-xs text-muted-foreground">location managers</p>
          </CardContent>
        </Card>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            All Users
            {loading && <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>}
          </CardTitle>
          <CardDescription>
            Manage user accounts and permissions • Real-time updates enabled
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Badge variant="outline" className="text-xs">
              {filteredUsers.length} of {totalUsers} users
            </Badge>
          </div>

          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-muted rounded-full animate-pulse"></div>
                    <div className="space-y-2">
                      <div className="w-32 h-4 bg-muted rounded animate-pulse"></div>
                      <div className="w-24 h-3 bg-muted rounded animate-pulse"></div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-6">
                    <div className="w-16 h-6 bg-muted rounded animate-pulse"></div>
                    <div className="w-20 h-4 bg-muted rounded animate-pulse"></div>
                    <div className="w-16 h-4 bg-muted rounded animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm ? "No users found matching your search." : "No users found."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src="" alt={user.name || user.username} />
                      <AvatarFallback>
                        {(user.name || user.username).split(" ").map(n => n[0]).join("").toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{user.name || user.username}</p>
                        {getRoleIcon(user.role)}
                      </div>
                      <p className="text-sm text-muted-foreground">@{user.username}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-6 text-sm">
                    <div className="text-center min-w-[80px]">
                      <p className="text-muted-foreground">Role</p>
                      <Badge variant={getRoleBadgeVariant(user.role)}>
                        {user.role}
                      </Badge>
                    </div>
                    <div className="text-center min-w-[100px]">
                      <p className="text-muted-foreground flex items-center gap-1 justify-center">
                        <MapPin className="h-3 w-3" />
                        Location
                      </p>
                      <p className="font-medium">
                        {user.location?.location || "Not assigned"}
                      </p>
                    </div>
                    <div className="text-center min-w-[100px]">
                      <p className="text-muted-foreground flex items-center gap-1 justify-center">
                        <Clock className="h-3 w-3" />
                        Last Login
                      </p>
                      <p className="font-medium">{formatDate(user.last_login)}</p>
                    </div>
                    <div className="text-center min-w-[80px]">
                      <p className="text-muted-foreground">Status</p>
                      <Badge variant={user.status === "active" ? "default" : "secondary"}>
                        <div className={`w-2 h-2 rounded-full mr-1 ${user.status === "active" ? "bg-green-500" : "bg-gray-500"}`}></div>
                        {user.status}
                      </Badge>
                    </div>
                    <div className="text-center min-w-[100px]">
                      <p className="text-muted-foreground flex items-center gap-1 justify-center">
                        <Calendar className="h-3 w-3" />
                        Created
                      </p>
                      <p className="font-medium text-xs">{new Date(user.created_at).toLocaleDateString()}</p>
                    </div>
                    <Button variant="outline" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}