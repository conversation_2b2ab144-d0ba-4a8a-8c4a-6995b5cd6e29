import { useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Clock, Users, DollarSign, RefreshCw, Filter, Search, Activity, CheckCircle2, XCircle } from "lucide-react";
import { useShiftData, type ShiftData } from "@/hooks/useDashboardData";

// Utility function to format currency
const formatCurrency = (amount: string | number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(Number(amount));
};

// Utility function to format date and time
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
};

// Utility function to calculate shift duration
const calculateShiftDuration = (startTime: string, endTime: string | null) => {
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  const diffMs = end.getTime() - start.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  return `${diffHours}h ${diffMinutes}m`;
};

export default function Attendance() {
  const { shifts, loading, error, refetch } = useShiftData();
  const [searchTerm, setSearchTerm] = useState("");
  const [locationFilter, setLocationFilter] = useState("all");
  const [operatorFilter, setOperatorFilter] = useState("all");
  const [salesFilter, setSalesFilter] = useState("all");

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const activeShifts = shifts.filter(shift => shift.status === 'active');
    const endedShifts = shifts.filter(shift => shift.status === 'ended');
    const totalSales = shifts.reduce((sum, shift) => sum + Number(shift.total_sales), 0);
    const totalTransactions = shifts.reduce((sum, shift) => sum + shift.total_transactions, 0);

    return {
      activeShifts: activeShifts.length,
      endedShifts: endedShifts.length,
      totalSales,
      totalTransactions,
    };
  }, [shifts]);

  // Get unique locations and operators for filters
  const uniqueLocations = useMemo(() => {
    const locations = [...new Set(shifts.map(shift => shift.location_name))];
    return locations.sort();
  }, [shifts]);

  const uniqueOperators = useMemo(() => {
    const operators = [...new Set(shifts.map(shift => shift.operator_name))];
    return operators.sort();
  }, [shifts]);

  // Filter shifts based on search and filter criteria
  const filteredShifts = useMemo(() => {
    return shifts.filter(shift => {
      const matchesSearch =
        shift.operator_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shift.location_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shift.shift_id.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesLocation = locationFilter === "all" || shift.location_name === locationFilter;
      const matchesOperator = operatorFilter === "all" || shift.operator_name === operatorFilter;

      let matchesSales = true;
      if (salesFilter === "high") {
        matchesSales = Number(shift.total_sales) > 500;
      } else if (salesFilter === "low") {
        matchesSales = Number(shift.total_sales) <= 500;
      }

      return matchesSearch && matchesLocation && matchesOperator && matchesSales;
    });
  }, [shifts, searchTerm, locationFilter, operatorFilter, salesFilter]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span>Loading shift data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-600 mb-2">Error Loading Shifts</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={refetch} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Shift Attendance</h2>
          <p className="text-muted-foreground">
            Real-time shift tracking with sales performance across all locations.
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={refetch} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Shifts</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summaryStats.activeShifts}</div>
            <p className="text-xs text-muted-foreground">currently working</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ended Shifts</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{summaryStats.endedShifts}</div>
            <p className="text-xs text-muted-foreground">completed today</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
            <DollarSign className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">{formatCurrency(summaryStats.totalSales)}</div>
            <p className="text-xs text-muted-foreground">across all shifts</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{summaryStats.totalTransactions}</div>
            <p className="text-xs text-muted-foreground">total transactions</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-5">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search shifts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={locationFilter} onValueChange={setLocationFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Locations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {uniqueLocations.map(location => (
                  <SelectItem key={location} value={location}>{location}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={operatorFilter} onValueChange={setOperatorFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Operators" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Operators</SelectItem>
                {uniqueOperators.map(operator => (
                  <SelectItem key={operator} value={operator}>{operator}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={salesFilter} onValueChange={setSalesFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Sales" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sales</SelectItem>
                <SelectItem value="high">Higher Sales (>$500)</SelectItem>
                <SelectItem value="low">Lower Sales (≤$500)</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setLocationFilter("all");
                setOperatorFilter("all");
                setSalesFilter("all");
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Shift Cards */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">
            Shift Details ({filteredShifts.length} {filteredShifts.length === 1 ? 'shift' : 'shifts'})
          </h3>
        </div>

        {filteredShifts.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Users className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold text-muted-foreground mb-2">No Shifts Found</h3>
              <p className="text-muted-foreground text-center">
                {shifts.length === 0
                  ? "No shift data available. Shifts will appear here once operators start working."
                  : "No shifts match your current filters. Try adjusting your search criteria."
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredShifts.map((shift) => (
              <Card
                key={shift.id}
                className={`relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02] ${
                  shift.status === 'active'
                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 dark:from-green-950/20 dark:to-emerald-950/20 dark:border-green-800'
                    : 'bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200 dark:from-gray-950/20 dark:to-slate-950/20 dark:border-gray-800'
                }`}
              >
                {/* Status Indicator */}
                <div className="absolute top-4 right-4">
                  {shift.status === 'active' ? (
                    <div className="flex items-center space-x-2">
                      <div className="relative">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        <div className="absolute inset-0 w-3 h-3 bg-green-500 rounded-full animate-ping opacity-75"></div>
                      </div>
                      <Badge variant="default" className="bg-green-600 hover:bg-green-700 text-white">
                        Active
                      </Badge>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <Badge variant="destructive" className="bg-red-600 hover:bg-red-700">
                        End
                      </Badge>
                    </div>
                  )}
                </div>

                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg font-bold text-gray-900 dark:text-gray-100">
                        {shift.operator_name}
                      </CardTitle>
                      <CardDescription className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {shift.location_name}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    ID: {shift.shift_id}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Time Information */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        Start Time
                      </div>
                      <p className="text-sm font-medium">{formatDateTime(shift.shift_start_time)}</p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {shift.status === 'active' ? 'Expected End' : 'End Time'}
                      </div>
                      <p className="text-sm font-medium">
                        {shift.status === 'active'
                          ? formatDateTime(shift.shift_end_time)
                          : shift.actual_end_time
                            ? formatDateTime(shift.actual_end_time)
                            : formatDateTime(shift.shift_end_time)
                        }
                      </p>
                    </div>
                  </div>

                  {/* Duration */}
                  <div className="space-y-1">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Activity className="h-3 w-3 mr-1" />
                      Duration
                    </div>
                    <p className="text-sm font-medium">
                      {calculateShiftDuration(
                        shift.shift_start_time,
                        shift.status === 'active' ? null : shift.actual_end_time || shift.shift_end_time
                      )}
                    </p>
                  </div>

                  {/* Sales Performance */}
                  <div className="bg-white dark:bg-gray-900 rounded-lg p-3 space-y-3">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                      <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                      Sales Performance
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center">
                        <p className="text-lg font-bold text-green-600">{formatCurrency(shift.total_sales)}</p>
                        <p className="text-xs text-muted-foreground">Total Sales</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-blue-600">{shift.total_transactions}</p>
                        <p className="text-xs text-muted-foreground">Transactions</p>
                      </div>
                    </div>

                    {/* Detailed Sales Breakdown */}
                    {(Number(shift.total_deli_sales) > 0 || Number(shift.total_theater_sales) > 0 || Number(shift.total_sale_sales) > 0) && (
                      <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          {Number(shift.total_deli_sales) > 0 && (
                            <div className="text-center">
                              <p className="font-medium text-orange-600">{formatCurrency(shift.total_deli_sales)}</p>
                              <p className="text-muted-foreground">Deli</p>
                            </div>
                          )}
                          {Number(shift.total_theater_sales) > 0 && (
                            <div className="text-center">
                              <p className="font-medium text-purple-600">{formatCurrency(shift.total_theater_sales)}</p>
                              <p className="text-muted-foreground">Theater</p>
                            </div>
                          )}
                          {Number(shift.total_sale_sales) > 0 && (
                            <div className="text-center">
                              <p className="font-medium text-indigo-600">{formatCurrency(shift.total_sale_sales)}</p>
                              <p className="text-muted-foreground">Sale</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Remaining Time for Active Shifts */}
                  {shift.status === 'active' && shift.remaining_time_minutes > 0 && (
                    <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          Time Remaining
                        </span>
                        <span className="text-sm font-bold text-blue-600">
                          {Math.floor(shift.remaining_time_minutes / 60)}h {shift.remaining_time_minutes % 60}m
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}