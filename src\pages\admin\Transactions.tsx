import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Search, Filter, Download, CreditCard, Banknote, Smartphone } from "lucide-react";

const transactions = [
  {
    id: "TXN-001",
    amount: "$45.99",
    customer: "John Doe",
    items: 3,
    method: "Credit Card",
    status: "Completed",
    shop: "Downtown",
    cashier: "<PERSON>",
    time: "2:34 PM",
  },
  {
    id: "TXN-002",
    amount: "$128.50",
    customer: "<PERSON>",
    items: 7,
    method: "Cash",
    status: "Completed",
    shop: "Mall",
    cashier: "Mike <PERSON>",
    time: "2:28 PM",
  },
  {
    id: "TXN-003",
    amount: "$89.99",
    customer: "<PERSON> Johnson",
    items: 2,
    method: "Mobile Pay",
    status: "Pending",
    shop: "Downtown",
    cashier: "<PERSON>",
    time: "2:15 PM",
  },
  {
    id: "TXN-004",
    amount: "$156.25",
    customer: "Alice Brown",
    items: 5,
    method: "Credit Card",
    status: "Completed",
    shop: "Airport",
    cashier: "<PERSON> S.",
    time: "1:45 PM",
  },
];

const getPaymentIcon = (method: string) => {
  switch (method) {
    case "Credit Card":
      return <CreditCard className="h-4 w-4" />;
    case "Cash":
      return <Banknote className="h-4 w-4" />;
    case "Mobile Pay":
      return <Smartphone className="h-4 w-4" />;
    default:
      return <CreditCard className="h-4 w-4" />;
  }
};

export default function Transactions() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Transactions</h2>
          <p className="text-muted-foreground">
            Monitor all sales transactions across your retail locations.
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Sales</CardTitle>
            <CreditCard className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">$12,420</div>
            <p className="text-xs text-muted-foreground">+15% from yesterday</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <Badge className="text-xs">156</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">transactions today</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Sale</CardTitle>
            <CreditCard className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$79.62</div>
            <p className="text-xs text-muted-foreground">per transaction</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Badge variant="destructive" className="text-xs">3</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">3</div>
            <p className="text-xs text-muted-foreground">need attention</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>All transactions from the last 24 hours</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search transactions..." className="pl-8" />
            </div>
          </div>

          {/* Transactions List */}
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                    {getPaymentIcon(transaction.method)}
                  </div>
                  <div>
                    <p className="font-medium">{transaction.id}</p>
                    <p className="text-sm text-muted-foreground">
                      {transaction.customer} • {transaction.items} items • {transaction.shop}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-right">
                    <p className="font-medium">{transaction.amount}</p>
                    <p className="text-muted-foreground">{transaction.method}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{transaction.time}</p>
                    <p className="text-muted-foreground">{transaction.cashier}</p>
                  </div>
                  <Badge variant={transaction.status === "Completed" ? "default" : "destructive"}>
                    {transaction.status}
                  </Badge>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}