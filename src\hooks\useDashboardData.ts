import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface DashboardStats {
  totalSales: number;
  totalRevenue: number;
  totalItemsSold: number;
  totalUsers: number;
  totalTickets: number;
  totalTicketRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
}

export interface SalesData {
  month: string;
  revenue: number;
  sales: number;
}

export interface TicketData {
  month: string;
  tickets: number;
  revenue: number;
}

export interface LocationPerformance {
  locationId: number;
  locationName: string;
  locationCode: string;
  monthlyRevenue: number;
  operatorCount: number;
  itemsSold: number;
  ticketRevenue: number;
  ticketsSold: number;
  totalRevenue: number;
}

export function useDashboardStats() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch sales data with better error handling
      const { data: salesData, error: salesError } = await supabase
        .from('sales')
        .select('total_amount, item_count, sale_date')
        .eq('status', 'completed');

      if (salesError) {
        console.error('Sales data fetch error:', salesError);
        throw new Error(`Failed to fetch sales data: ${salesError.message}`);
      }

      // Fetch items sold data with error handling
      const { data: itemsData, error: itemsError } = await supabase
        .from('sales_items')
        .select('quantity');

      if (itemsError) {
        console.error('Items data fetch error:', itemsError);
        throw new Error(`Failed to fetch items data: ${itemsError.message}`);
      }

      // Fetch users data with error handling
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id')
        .eq('status', 'active');

      if (usersError) {
        console.error('Users data fetch error:', usersError);
        throw new Error(`Failed to fetch users data: ${usersError.message}`);
      }

      // Fetch tickets data with error handling
      const { data: ticketsData, error: ticketsError } = await supabase
        .from('tickets')
        .select('total_amount, issued_at')
        .eq('status', 'active');

      if (ticketsError) {
        console.error('Tickets data fetch error:', ticketsError);
        throw new Error(`Failed to fetch tickets data: ${ticketsError.message}`);
      }

      // Calculate statistics
      const totalSales = salesData?.length || 0;
      const totalRevenue = salesData?.reduce((sum, sale) => sum + Number(sale.total_amount), 0) || 0;
      const totalItemsSold = itemsData?.reduce((sum, item) => sum + item.quantity, 0) || 0;
      const totalUsers = usersData?.length || 0;
      const totalTickets = ticketsData?.length || 0;
      const totalTicketRevenue = ticketsData?.reduce((sum, ticket) => sum + Number(ticket.total_amount), 0) || 0;

      // Calculate monthly revenue (current month)
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthlyRevenue = salesData?.filter(sale => {
        const saleDate = new Date(sale.sale_date || '');
        return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
      }).reduce((sum, sale) => sum + Number(sale.total_amount), 0) || 0;

      // Calculate growth (mock calculation for now)
      const revenueGrowth = monthlyRevenue > 0 ? 12.5 : 0;

      setStats({
        totalSales,
        totalRevenue,
        totalItemsSold,
        totalUsers,
        totalTickets,
        totalTicketRevenue,
        monthlyRevenue,
        revenueGrowth,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return { stats, loading, error, refetch: fetchDashboardStats };
}

export function useSalesChartData() {
  const [data, setData] = useState<SalesData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSalesChartData();
  }, []);

  const fetchSalesChartData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: salesData, error: salesError } = await supabase
        .from('sales')
        .select('total_amount, sale_date')
        .eq('status', 'completed')
        .order('sale_date', { ascending: true });

      if (salesError) throw salesError;

      // Group data by month and ensure we have data for recent months
      const monthlyData: { [key: string]: { revenue: number; sales: number } } = {};

      // Generate last 6 months to ensure we have data points
      const months = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        months.push(monthKey);
        monthlyData[monthKey] = { revenue: 0, sales: 0 };
      }

      salesData?.forEach(sale => {
        const date = new Date(sale.sale_date || '');
        const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

        if (monthlyData[monthKey] !== undefined) {
          monthlyData[monthKey].revenue += Number(sale.total_amount);
          monthlyData[monthKey].sales += 1;
        }
      });

      const chartData = months.map(month => ({
        month,
        revenue: Math.round(monthlyData[month].revenue * 100) / 100, // Round to 2 decimal places
        sales: monthlyData[month].sales,
      }));

      setData(chartData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return { data, loading, error, refetch: fetchSalesChartData };
}

export function useTicketChartData() {
  const [data, setData] = useState<TicketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTicketChartData();
  }, []);

  const fetchTicketChartData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: ticketsData, error: ticketsError } = await supabase
        .from('tickets')
        .select('total_amount, issued_at')
        .eq('status', 'active')
        .order('issued_at', { ascending: true });

      if (ticketsError) throw ticketsError;

      // Group data by month and ensure we have data for recent months
      const monthlyData: { [key: string]: { revenue: number; tickets: number } } = {};

      // Generate last 6 months to ensure we have data points
      const months = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        months.push(monthKey);
        monthlyData[monthKey] = { revenue: 0, tickets: 0 };
      }

      ticketsData?.forEach(ticket => {
        const date = new Date(ticket.issued_at || '');
        const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

        if (monthlyData[monthKey] !== undefined) {
          monthlyData[monthKey].revenue += Number(ticket.total_amount);
          monthlyData[monthKey].tickets += 1;
        }
      });

      const chartData = months.map(month => ({
        month,
        tickets: monthlyData[month].tickets,
        revenue: Math.round(monthlyData[month].revenue * 100) / 100, // Round to 2 decimal places
      }));

      setData(chartData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return { data, loading, error, refetch: fetchTicketChartData };
}

export function useLocationPerformance() {
  const [data, setData] = useState<LocationPerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLocationPerformance = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current month for filtering
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();
      const monthStart = new Date(currentYear, currentMonth - 1, 1).toISOString();
      const monthEnd = new Date(currentYear, currentMonth, 0, 23, 59, 59).toISOString();

      // Fetch all locations
      const { data: locationsData, error: locationsError } = await supabase
        .from('locations')
        .select('id, location, location_code')
        .eq('status', 'active');

      if (locationsError) {
        throw new Error(`Error fetching locations: ${locationsError.message}`);
      }

      if (!locationsData || locationsData.length === 0) {
        setData([]);
        return;
      }

      const locationPerformance: LocationPerformance[] = [];

      for (const location of locationsData) {
        // Fetch monthly sales revenue for this location
        const { data: salesData, error: salesError } = await supabase
          .from('sales')
          .select('total_amount, item_count')
          .eq('location_id', location.id)
          .eq('status', 'completed')
          .gte('sale_date', monthStart)
          .lte('sale_date', monthEnd);

        if (salesError) {
          console.error(`Error fetching sales for location ${location.id}:`, salesError);
        }

        // Fetch total items sold for this location (all time)
        const { data: salesItemsData, error: salesItemsError } = await supabase
          .from('sales_items')
          .select('quantity, sale_id')
          .in('sale_id',
            (await supabase
              .from('sales')
              .select('sale_id')
              .eq('location_id', location.id)
              .eq('status', 'completed')
            ).data?.map(s => s.sale_id) || []
          );

        if (salesItemsError) {
          console.error(`Error fetching sales items for location ${location.id}:`, salesItemsError);
        }

        // Fetch ticket data for this location
        const { data: ticketsData, error: ticketsError } = await supabase
          .from('tickets')
          .select('total_amount')
          .eq('location_id', location.id)
          .eq('status', 'active')
          .gte('issued_at', monthStart)
          .lte('issued_at', monthEnd);

        if (ticketsError) {
          console.error(`Error fetching tickets for location ${location.id}:`, ticketsError);
        }

        // Fetch operator count for this location
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('id')
          .eq('location_id', location.id)
          .eq('status', 'active');

        if (usersError) {
          console.error(`Error fetching users for location ${location.id}:`, usersError);
        }

        // Calculate metrics
        const monthlyRevenue = salesData?.reduce((sum, sale) => sum + Number(sale.total_amount), 0) || 0;
        const itemsSold = salesItemsData?.reduce((sum, item) => sum + Number(item.quantity), 0) || 0;
        const ticketRevenue = ticketsData?.reduce((sum, ticket) => sum + Number(ticket.total_amount), 0) || 0;
        const ticketsSold = ticketsData?.length || 0;
        const operatorCount = usersData?.length || 0;
        const totalRevenue = monthlyRevenue + ticketRevenue;

        locationPerformance.push({
          locationId: location.id,
          locationName: location.location,
          locationCode: location.location_code,
          monthlyRevenue: Math.round(monthlyRevenue * 100) / 100,
          operatorCount,
          itemsSold,
          ticketRevenue: Math.round(ticketRevenue * 100) / 100,
          ticketsSold,
          totalRevenue: Math.round(totalRevenue * 100) / 100,
        });
      }

      setData(locationPerformance);
    } catch (err) {
      console.error('Error fetching location performance:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch location performance');
    } finally {
      setLoading(false);
    }
  };

  const refetch = () => {
    fetchLocationPerformance();
  };

  useEffect(() => {
    fetchLocationPerformance();
  }, []);

  return { data, loading, error, refetch };
}

// Shift data interface
export interface ShiftData {
  id: number;
  shift_id: string;
  user_id: number;
  location_id: number;
  operator_name: string;
  location_name: string;
  shift_start_time: string;
  shift_end_time: string;
  actual_end_time: string | null;
  shift_duration_hours: number;
  remaining_time_minutes: number;
  status: 'active' | 'ended' | 'expired';
  total_sales: string;
  total_transactions: number;
  total_sale_sales: string;
  total_sale_transactions: number;
  total_theater_sales: string;
  total_theater_transactions: number;
  total_deli_sales: string;
  total_deli_transactions: number;
  created_at: string;
  updated_at: string;
}

// Hook for fetching shift data with real-time synchronization
export function useShiftData() {
  const [shifts, setShifts] = useState<ShiftData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchShifts = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('shifts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setShifts(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch shifts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchShifts();

    // Set up real-time subscription
    const subscription = supabase
      .channel('shifts-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'shifts'
        },
        (payload) => {
          console.log('Shift change received:', payload);
          fetchShifts(); // Refetch all data when changes occur
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { shifts, loading, error, refetch: fetchShifts };
}
