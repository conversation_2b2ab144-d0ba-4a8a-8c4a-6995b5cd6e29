import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const recentSales = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    amount: "+$1,999.00",
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    amount: "+$39.00",
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    amount: "+$299.00",
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    amount: "+$99.00",
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    amount: "+$39.00",
  },
];

export function RecentSales() {
  return (
    <div className="space-y-8">
      {recentSales.map((sale, index) => (
        <div key={index} className="flex items-center">
          <Avatar className="h-9 w-9">
            <AvatarImage src="" alt={sale.name} />
            <AvatarFallback>
              {sale.name.split(" ").map(n => n[0]).join("").toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{sale.name}</p>
            <p className="text-sm text-muted-foreground">{sale.email}</p>
          </div>
          <div className="ml-auto font-medium">{sale.amount}</div>
        </div>
      ))}
    </div>
  );
}