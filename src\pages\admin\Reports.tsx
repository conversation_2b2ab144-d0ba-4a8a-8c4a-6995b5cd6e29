import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Download, FileText, BarChart3, TrendingUp, Filter, MapPin, Clock, User, Search, RefreshCw } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

// Interfaces for type safety
interface Location {
  id: number;
  location: string;
  location_code: string;
}

interface User {
  id: number;
  name: string;
  username: string;
}

interface ReportData {
  date: string;
  dayShift: {
    cash: number;
    debit: number;
    credit: number;
    total: number;
  };
  nightShift: {
    cash: number;
    debit: number;
    credit: number;
    total: number;
  };
  grandTotal: {
    cash: number;
    debit: number;
    credit: number;
    total: number;
  };
}

interface ReportSection {
  title: string;
  icon: string;
  data: ReportData[];
  totalAmount: number;
}

export default function Reports() {
  const [locations, setLocations] = useState<Location[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<{
    theater: ReportSection;
    deli: ReportSection;
    regular: ReportSection;
  } | null>(null);

  // Filter states
  const [selectedLocation, setSelectedLocation] = useState<string>("all");
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<string>("daily");
  const [selectedShift, setSelectedShift] = useState<string>("all");
  const [selectedOperator, setSelectedOperator] = useState<string>("all");
  const [operatorSearch, setOperatorSearch] = useState<string>("");
  const [dateRange, setDateRange] = useState({
    start: new Date().toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });

  const { toast } = useToast();

  // Fetch initial data
  useEffect(() => {
    fetchLocations();
    fetchUsers();
  }, []);

  // Auto-generate report when filters change
  useEffect(() => {
    if (locations.length > 0) {
      generateReport();
    }
  }, [selectedLocation, selectedTimeFrame, selectedShift, selectedOperator, dateRange, locations]);

  const fetchLocations = async () => {
    try {
      const { data, error } = await supabase
        .from('locations')
        .select('id, location, location_code')
        .eq('status', 'active')
        .order('location');

      if (error) throw error;
      setLocations(data || []);
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch locations",
        variant: "destructive",
      });
    }
  };

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, username')
        .eq('status', 'active')
        .order('name');

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    }
  };

  const generateReport = async () => {
    setLoading(true);
    try {
      // Fetch Theater (Tickets) data
      let ticketsQuery = supabase
        .from('tickets')
        .select(`
          *,
          locations:location_id (location, location_code)
        `)
        .gte('issued_at', `${dateRange.start}T00:00:00Z`)
        .lte('issued_at', `${dateRange.end}T23:59:59Z`);

      if (selectedLocation !== 'all') {
        ticketsQuery = ticketsQuery.eq('location_id', selectedLocation);
      }

      if (selectedOperator !== 'all') {
        ticketsQuery = ticketsQuery.eq('user_id', selectedOperator);
      }

      const { data: ticketsData, error: ticketsError } = await ticketsQuery;
      if (ticketsError) throw ticketsError;

      // Fetch Deli sales data
      let deliQuery = supabase
        .from('sales')
        .select(`
          *,
          locations:location_id (location, location_code)
        `)
        .eq('sale_type', 'deli')
        .gte('sale_date', `${dateRange.start}T00:00:00Z`)
        .lte('sale_date', `${dateRange.end}T23:59:59Z`);

      if (selectedLocation !== 'all') {
        deliQuery = deliQuery.eq('location_id', selectedLocation);
      }

      if (selectedOperator !== 'all') {
        deliQuery = deliQuery.eq('user_id', selectedOperator);
      }

      const { data: deliData, error: deliError } = await deliQuery;
      if (deliError) throw deliError;

      // Fetch Regular sales data
      let regularQuery = supabase
        .from('sales')
        .select(`
          *,
          locations:location_id (location, location_code)
        `)
        .eq('sale_type', 'sale')
        .gte('sale_date', `${dateRange.start}T00:00:00Z`)
        .lte('sale_date', `${dateRange.end}T23:59:59Z`);

      if (selectedLocation !== 'all') {
        regularQuery = regularQuery.eq('location_id', selectedLocation);
      }

      if (selectedOperator !== 'all') {
        regularQuery = regularQuery.eq('user_id', selectedOperator);
      }

      const { data: regularData, error: regularError } = await regularQuery;
      if (regularError) throw regularError;

      // Apply shift filtering if needed
      const filterByShift = (data: any[], dateField: string) => {
        if (selectedShift === 'all') return data;

        return data.filter(item => {
          const hour = new Date(item[dateField]).getHours();
          const isNightShift = hour >= 18 || hour < 6; // 6 PM to 6 AM is night shift

          if (selectedShift === 'day') return !isNightShift;
          if (selectedShift === 'night') return isNightShift;
          return true;
        });
      };

      // Process and format the data
      const processedData = {
        theater: processReportData(filterByShift(ticketsData || [], 'issued_at'), 'theater'),
        deli: processReportData(filterByShift(deliData || [], 'sale_date'), 'deli'),
        regular: processReportData(filterByShift(regularData || [], 'sale_date'), 'regular')
      };

      setReportData(processedData);

      toast({
        title: "Success",
        description: "Report generated successfully",
      });

    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: "Error",
        description: "Failed to generate report",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const processReportData = (data: any[], type: string): ReportSection => {
    // Group data by date and shift
    const groupedData: { [key: string]: any } = {};
    let totalAmount = 0;

    data.forEach(item => {
      const date = new Date(type === 'theater' ? item.issued_at : item.sale_date).toISOString().split('T')[0];
      const hour = new Date(type === 'theater' ? item.issued_at : item.sale_date).getHours();
      const isNightShift = hour >= 18 || hour < 6; // 6 PM to 6 AM is night shift

      if (!groupedData[date]) {
        groupedData[date] = {
          date,
          dayShift: { cash: 0, debit: 0, credit: 0, total: 0 },
          nightShift: { cash: 0, debit: 0, credit: 0, total: 0 },
          grandTotal: { cash: 0, debit: 0, credit: 0, total: 0 }
        };
      }

      const shift = isNightShift ? 'nightShift' : 'dayShift';
      const amount = parseFloat(type === 'theater' ? item.total_amount : item.total_amount);

      // Determine payment method
      if (type === 'theater') {
        if (item.payment_method === 'cash') {
          groupedData[date][shift].cash += amount;
        } else if (item.payment_method === 'debit') {
          groupedData[date][shift].debit += amount;
        } else if (item.payment_method === 'credit') {
          groupedData[date][shift].credit += amount;
        }
      } else {
        groupedData[date][shift].cash += parseFloat(item.payment_cash || 0);
        groupedData[date][shift].debit += parseFloat(item.payment_debit || 0);
        groupedData[date][shift].credit += parseFloat(item.payment_credit || 0);
      }

      groupedData[date][shift].total += amount;
      totalAmount += amount;
    });

    // Calculate grand totals for each date
    Object.keys(groupedData).forEach(date => {
      const dayShift = groupedData[date].dayShift;
      const nightShift = groupedData[date].nightShift;

      groupedData[date].grandTotal = {
        cash: dayShift.cash + nightShift.cash,
        debit: dayShift.debit + nightShift.debit,
        credit: dayShift.credit + nightShift.credit,
        total: dayShift.total + nightShift.total
      };
    });

    return {
      title: type === 'theater' ? 'Theater' : type === 'deli' ? 'Deli' : 'Regular Sales',
      icon: type === 'theater' ? '🎭' : type === 'deli' ? '🥪' : '🛒',
      data: Object.values(groupedData),
      totalAmount
    };
  };

  const calculateShiftTotal = (data: ReportData[], shiftType: 'dayShift' | 'nightShift'): number => {
    return data.reduce((total, dayData) => {
      return total + dayData[shiftType].total;
    }, 0);
  };

  const renderReportSection = (section: ReportSection, type: string) => {
    const getTypeColor = (type: string) => {
      switch (type) {
        case 'theater': return 'bg-blue-50 border-blue-200';
        case 'deli': return 'bg-orange-50 border-orange-200';
        case 'regular': return 'bg-green-50 border-green-200';
        default: return 'bg-gray-50 border-gray-200';
      }
    };

    const getTypeIcon = (type: string) => {
      switch (type) {
        case 'theater': return '🎭';
        case 'deli': return '🥪';
        case 'regular': return '🛒';
        default: return '📊';
      }
    };

    return (
      <Card key={type} className={`${getTypeColor(type)} border-2`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">{getTypeIcon(type)}</span>
              {section.title}
            </CardTitle>
            <Badge className={`text-lg px-3 py-1 ${
              type === 'theater' ? 'bg-blue-600' :
              type === 'deli' ? 'bg-orange-600' : 'bg-green-600'
            }`}>
              ${section.totalAmount.toFixed(2)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {section.data.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No data available for the selected filters
            </div>
          ) : (
            <div className="space-y-4">
              {section.data.map((dayData, index) => (
                <div key={index} className="border rounded-lg overflow-hidden">
                  {/* Date Header */}
                  <div className="bg-gray-100 px-4 py-2 border-b">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">📅 Date</span>
                      <span className="text-sm text-muted-foreground">
                        {new Date(dayData.date).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {/* Report Table */}
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3 bg-blue-100 text-blue-800">
                            ☀️ DAY SHIFT
                          </th>
                          <th className="text-left p-3 bg-gray-800 text-white">
                            🌙 NIGHT SHIFT
                          </th>
                          <th className="text-left p-3 bg-green-600 text-white">
                            📊 TOTAL
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="p-0">
                            <div className="grid grid-cols-4 text-sm">
                              <div className="p-2 bg-blue-50 border-r flex items-center gap-1">
                                <span>💵</span> Cash
                              </div>
                              <div className="p-2 bg-yellow-50 border-r flex items-center gap-1">
                                <span>💳</span> Debit
                              </div>
                              <div className="p-2 bg-purple-50 border-r flex items-center gap-1">
                                <span>💎</span> Credit
                              </div>
                              <div className="p-2 bg-blue-100 font-medium flex items-center gap-1">
                                <span>📊</span> Day Total
                              </div>
                            </div>
                          </td>
                          <td className="p-0">
                            <div className="grid grid-cols-4 text-sm">
                              <div className="p-2 bg-gray-100 border-r flex items-center gap-1">
                                <span>💵</span> Cash
                              </div>
                              <div className="p-2 bg-gray-200 border-r flex items-center gap-1">
                                <span>💳</span> Debit
                              </div>
                              <div className="p-2 bg-gray-300 border-r flex items-center gap-1">
                                <span>💎</span> Credit
                              </div>
                              <div className="p-2 bg-gray-800 text-white font-medium flex items-center gap-1">
                                <span>📊</span> Night Total
                              </div>
                            </div>
                          </td>
                          <td className="p-0">
                            <div className="grid grid-cols-4 text-sm">
                              <div className="p-2 bg-green-100 border-r flex items-center gap-1">
                                <span>💵</span> Cash
                              </div>
                              <div className="p-2 bg-green-200 border-r flex items-center gap-1">
                                <span>💳</span> Debit
                              </div>
                              <div className="p-2 bg-green-300 border-r flex items-center gap-1">
                                <span>💎</span> Credit
                              </div>
                              <div className="p-2 bg-green-600 text-white font-medium flex items-center gap-1">
                                <span>🔥</span> Grand Total
                              </div>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td className="p-0">
                            <div className="grid grid-cols-4 text-sm font-medium">
                              <div className="p-2 border-r text-green-700">
                                ${dayData.dayShift.cash.toFixed(2)}
                              </div>
                              <div className="p-2 border-r text-blue-700">
                                ${dayData.dayShift.debit.toFixed(2)}
                              </div>
                              <div className="p-2 border-r text-purple-700">
                                ${dayData.dayShift.credit.toFixed(2)}
                              </div>
                              <div className="p-2 bg-blue-100 font-bold text-blue-800">
                                ${dayData.dayShift.total.toFixed(2)}
                              </div>
                            </div>
                          </td>
                          <td className="p-0">
                            <div className="grid grid-cols-4 text-sm font-medium">
                              <div className="p-2 border-r text-green-700">
                                ${dayData.nightShift.cash.toFixed(2)}
                              </div>
                              <div className="p-2 border-r text-blue-700">
                                ${dayData.nightShift.debit.toFixed(2)}
                              </div>
                              <div className="p-2 border-r text-purple-700">
                                ${dayData.nightShift.credit.toFixed(2)}
                              </div>
                              <div className="p-2 bg-gray-800 text-white font-bold">
                                ${dayData.nightShift.total.toFixed(2)}
                              </div>
                            </div>
                          </td>
                          <td className="p-0">
                            <div className="grid grid-cols-4 text-sm font-medium">
                              <div className="p-2 border-r text-green-700">
                                ${dayData.grandTotal.cash.toFixed(2)}
                              </div>
                              <div className="p-2 border-r text-blue-700">
                                ${dayData.grandTotal.debit.toFixed(2)}
                              </div>
                              <div className="p-2 border-r text-purple-700">
                                ${dayData.grandTotal.credit.toFixed(2)}
                              </div>
                              <div className="p-2 bg-green-600 text-white font-bold">
                                ${dayData.grandTotal.total.toFixed(2)}
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Reports</h2>
          <p className="text-muted-foreground">
            Comprehensive business reports with advanced filtering • Real-time data
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={generateReport} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Advanced Filtering System */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Report Filters
          </CardTitle>
          <CardDescription>
            Filter reports by location, time frame, shift, and operator for detailed analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Location Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                Location
              </label>
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger>
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id.toString()}>
                      {location.location} ({location.location_code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Time Frame Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <Clock className="h-4 w-4" />
                Time Frame
              </label>
              <Select value={selectedTimeFrame} onValueChange={setSelectedTimeFrame}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time frame" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Shift Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Shift</label>
              <Select value={selectedShift} onValueChange={setSelectedShift}>
                <SelectTrigger>
                  <SelectValue placeholder="Select shift" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Shifts</SelectItem>
                  <SelectItem value="day">Day Shift</SelectItem>
                  <SelectItem value="night">Night Shift</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Operator Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <User className="h-4 w-4" />
                Operator
              </label>
              <Select value={selectedOperator} onValueChange={setSelectedOperator}>
                <SelectTrigger>
                  <SelectValue placeholder="Select operator" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Operators</SelectItem>
                  {users
                    .filter(user =>
                      user.name?.toLowerCase().includes(operatorSearch.toLowerCase()) ||
                      user.username.toLowerCase().includes(operatorSearch.toLowerCase())
                    )
                    .map((user) => (
                    <SelectItem key={user.id} value={user.id.toString()}>
                      {user.name || user.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date Range */}
          <div className="grid gap-4 md:grid-cols-2 mt-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <Input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <Input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              />
            </div>
          </div>

          {/* Quick Operator Search */}
          <div className="mt-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Quick search operators..."
                className="pl-8"
                value={operatorSearch}
                onChange={(e) => setOperatorSearch(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generated Reports Display */}
      {reportData && (
        <div className="space-y-6">
          {/* Report Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    📅 {new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </CardTitle>
                  <CardDescription>
                    Report Period: {dateRange.start} to {dateRange.end}
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">🎭 THEATER</Badge>
                  <Badge variant="outline" className="bg-orange-50 text-orange-700">🥪 DELI</Badge>
                  <Badge variant="outline" className="bg-green-50 text-green-700">🛒 REGULAR</Badge>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Statistics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Theater Statistics Card */}
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-blue-800">
                  <span className="text-2xl">🎭</span>
                  Theater Sales
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-900">
                    ${reportData.theater.totalAmount.toFixed(2)}
                  </div>
                  <div className="text-sm text-blue-600">Total Sales</div>
                </div>
                <div className="grid grid-cols-2 gap-3 pt-2 border-t border-blue-200">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-800">
                      ${calculateShiftTotal(reportData.theater.data, 'dayShift').toFixed(2)}
                    </div>
                    <div className="text-xs text-blue-600 flex items-center justify-center gap-1">
                      <span>☀️</span> Day Shift
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-800">
                      ${calculateShiftTotal(reportData.theater.data, 'nightShift').toFixed(2)}
                    </div>
                    <div className="text-xs text-blue-600 flex items-center justify-center gap-1">
                      <span>🌙</span> Night Shift
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Deli Statistics Card */}
            <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-orange-800">
                  <span className="text-2xl">🥪</span>
                  Deli Items
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-900">
                    ${reportData.deli.totalAmount.toFixed(2)}
                  </div>
                  <div className="text-sm text-orange-600">Total Sales</div>
                </div>
                <div className="grid grid-cols-2 gap-3 pt-2 border-t border-orange-200">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-orange-800">
                      ${calculateShiftTotal(reportData.deli.data, 'dayShift').toFixed(2)}
                    </div>
                    <div className="text-xs text-orange-600 flex items-center justify-center gap-1">
                      <span>☀️</span> Day Shift
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-orange-800">
                      ${calculateShiftTotal(reportData.deli.data, 'nightShift').toFixed(2)}
                    </div>
                    <div className="text-xs text-orange-600 flex items-center justify-center gap-1">
                      <span>🌙</span> Night Shift
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Regular Sales Statistics Card */}
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-green-800">
                  <span className="text-2xl">🛒</span>
                  Regular Sales
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-900">
                    ${reportData.regular.totalAmount.toFixed(2)}
                  </div>
                  <div className="text-sm text-green-600">Total Sales</div>
                </div>
                <div className="grid grid-cols-2 gap-3 pt-2 border-t border-green-200">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-800">
                      ${calculateShiftTotal(reportData.regular.data, 'dayShift').toFixed(2)}
                    </div>
                    <div className="text-xs text-green-600 flex items-center justify-center gap-1">
                      <span>☀️</span> Day Shift
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-800">
                      ${calculateShiftTotal(reportData.regular.data, 'nightShift').toFixed(2)}
                    </div>
                    <div className="text-xs text-green-600 flex items-center justify-center gap-1">
                      <span>🌙</span> Night Shift
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Overall Summary Card */}
          <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-800">
                <span className="text-2xl">📊</span>
                Overall Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-900">
                    ${(reportData.theater.totalAmount + reportData.deli.totalAmount + reportData.regular.totalAmount).toFixed(2)}
                  </div>
                  <div className="text-sm text-purple-600">Grand Total</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-semibold text-purple-800">
                    ${(calculateShiftTotal(reportData.theater.data, 'dayShift') +
                       calculateShiftTotal(reportData.deli.data, 'dayShift') +
                       calculateShiftTotal(reportData.regular.data, 'dayShift')).toFixed(2)}
                  </div>
                  <div className="text-sm text-purple-600 flex items-center justify-center gap-1">
                    <span>☀️</span> Total Day Shift
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-semibold text-purple-800">
                    ${(calculateShiftTotal(reportData.theater.data, 'nightShift') +
                       calculateShiftTotal(reportData.deli.data, 'nightShift') +
                       calculateShiftTotal(reportData.regular.data, 'nightShift')).toFixed(2)}
                  </div>
                  <div className="text-sm text-purple-600 flex items-center justify-center gap-1">
                    <span>🌙</span> Total Night Shift
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-semibold text-purple-800">
                    {reportData.theater.data.length + reportData.deli.data.length + reportData.regular.data.length}
                  </div>
                  <div className="text-sm text-purple-600">Total Records</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Theater Section */}
          {renderReportSection(reportData.theater, 'theater')}

          {/* Deli Section */}
          {renderReportSection(reportData.deli, 'deli')}

          {/* Regular Sales Section */}
          {renderReportSection(reportData.regular, 'regular')}
        </div>
      )}

      {loading && (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Generating report...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {!reportData && !loading && (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                Select your filters and the report will be generated automatically
              </p>
            </div>
          </CardContent>
        </Card>
      )}

    </div>
  );
}